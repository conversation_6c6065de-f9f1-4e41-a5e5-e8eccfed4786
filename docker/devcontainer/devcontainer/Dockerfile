# Start with Ubuntu 24.04 and Python 3.12.1
FROM python:3.12.1-slim

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    unzip \
    jq \
    git \
    sudo \
    ca-certificates \
    gnupg \
    && rm -rf /var/lib/apt/lists/* 

# Install specific Node.js version
RUN mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_22.x nodistro main" > /etc/apt/sources.list.d/nodesource.list \
    && apt-get update && apt-get install -y nodejs=22.14.0* \
    && rm -rf /var/lib/apt/lists/* \
    && rm /bin/python3 \
    && rm -rf /bin/python3.11

# Install AWS CLI v2
RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64-2.24.10.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -rf aws awscliv2.zip

# Install Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/download/v2.37.1/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose \
    && chmod +x /usr/local/bin/docker-compose

# Create a non-root user and configure permissions
RUN useradd -m -s /bin/bash vscode \
    && echo "vscode ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set working directory
WORKDIR /workspace

# Create user npm directory for global packages
RUN mkdir -p /home/<USER>/.npm-global \
    && chown -R vscode:vscode /home/<USER>/.npm-global \
    && chown -R vscode:vscode /workspace

# Switch to vscode user
USER vscode

# Configure npm prefix for global installs
RUN npm config set prefix '/home/<USER>/.npm-global'

# Add global npm bin to PATH
ENV PATH="/home/<USER>/.npm-global/bin:/workspace/node_modules/.bin:$PATH"

# Initialize npm project
RUN npm init -y

# Install dependencies
RUN npm install --save-exact aws-cdk@2.1000.2

# Install global dev tools
RUN npm install -g typescript@latest @anthropic-ai/claude-code

# Acknowledge CDK warnings
RUN npx cdk acknowledge 32775 \
    && npx cdk acknowledge 34892