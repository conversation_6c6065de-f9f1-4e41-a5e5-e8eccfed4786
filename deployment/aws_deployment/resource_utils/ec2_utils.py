from typing import Any, Dict, List

import boto3
from aws_cdk import CfnOutput, RemovalPolicy, <PERSON><PERSON>
from aws_cdk import aws_ec2 as ec2
from aws_cdk import aws_iam as iam
from constructs import Construct

from deployment.aws_deployment.resource_utils.ssm_utils import create_ssm_parameter


def find_existing_persistent_volume(
    volume_id: str, stack_name: str = "prefect-dev-us-east-2", region: str = "us-east-2"
) -> str:
    """
    Find an existing EBS volume by searching for volumes from the same CloudFormation stack.

    Args:
        volume_id: The logical volume ID to search for (not used currently, kept for compatibility)
        stack_name: The CloudFormation stack name to search in
        region: AWS region to search in

    Returns:
        The physical volume ID if found, None otherwise
    """
    try:
        ec2_client = boto3.client("ec2", region_name=region)

        # Search for volumes from the same CloudFormation stack that are 10GB (our DB volumes)
        response = ec2_client.describe_volumes(
            Filters=[
                {"Name": "tag:aws:cloudformation:stack-name", "Values": [stack_name]},
                {"Name": "status", "Values": ["available", "in-use"]},
                {"Name": "size", "Values": ["10"]},  # Our DB volumes are 10GB
            ]
        )

        # Filter for volumes that look like database volumes (exclude root volumes)
        db_volumes = []
        for volume in response["Volumes"]:
            # Check if this is likely a database volume by looking at tags
            tags = {tag["Key"]: tag["Value"] for tag in volume.get("Tags", [])}
            logical_id = tags.get("aws:cloudformation:logical-id", "")

            # Look for volumes with logical IDs containing "db" or "volume" or "prefect"
            if any(
                keyword in logical_id.lower() for keyword in ["db", "volume", "prefect"]
            ):
                db_volumes.append(volume)

        # Return the most recent volume (by creation time) if multiple exist
        if db_volumes:
            volumes = sorted(db_volumes, key=lambda x: x["CreateTime"], reverse=True)
            return volumes[0]["VolumeId"]

    except Exception as e:
        print(f"Warning: Could not search for existing volumes: {e}")

    return None


def create_persistent_ebs_volume_from_config(
    scope: Construct,
    volume_config: Dict[str, Any],
    availability_zone: str,
    deployment_env: str = "dev",
    stack_name: str = None,
) -> ec2.Volume:
    """
    Create a persistent EBS volume that survives EC2 instance termination.

    Args:
        scope: The CDK construct scope
        volume_config: Volume configuration from aws_config.yaml
        availability_zone: AZ where the volume should be created
        deployment_env: Deployment environment (dev/prod)

    Returns:
        The created EBS volume
    """
    volume_id = volume_config.get("id")
    volume_size_gb = volume_config.get("size", 10)
    volume_size = Size.gibibytes(volume_size_gb)  # Convert integer to Size object
    volume_type_str = volume_config.get("type", "gp3").lower()

    # Map string volume type to CDK enum
    volume_type_mapping = {
        "gp2": ec2.EbsDeviceVolumeType.GP2,
        "gp3": ec2.EbsDeviceVolumeType.GP3,
        "io1": ec2.EbsDeviceVolumeType.IO1,
        "io2": ec2.EbsDeviceVolumeType.IO2,
        "st1": ec2.EbsDeviceVolumeType.ST1,
        "sc1": ec2.EbsDeviceVolumeType.SC1,
    }
    volume_type = volume_type_mapping.get(volume_type_str, ec2.EbsDeviceVolumeType.GP3)

    # Check if an existing volume exists first
    if stack_name:
        existing_volume_id = find_existing_persistent_volume(volume_id, stack_name)
    else:
        existing_volume_id = find_existing_persistent_volume(volume_id)

    if existing_volume_id:
        # Import existing volume
        print(f"Found existing EBS volume: {existing_volume_id}, importing it...")
        volume = ec2.Volume.from_volume_attributes(
            scope,
            f"{volume_id}-imported",
            volume_id=existing_volume_id,
            availability_zone=availability_zone,
        )
    else:
        # Create new EBS volume with proper tags for identification
        print(f"Creating new EBS volume: {volume_id}")
        volume = ec2.Volume(
            scope,
            volume_id,
            availability_zone=availability_zone,
            size=volume_size,
            volume_type=volume_type,
            removal_policy=RemovalPolicy.RETAIN,  # Keep volume when stack is deleted
        )

    # Add tags to identify the volume
    volume.node.add_metadata("aws:cdk:path", f"{scope.node.path}/{volume_id}")

    # Create SSM parameter to store volume ID for easy retrieval if configured
    if volume_config.get("create-parameter-store-variable-with-arn"):
        create_ssm_parameter(
            scope,
            config=volume_config,
            value=volume.volume_id,
        )

    # Output the volume ID for reference (only for new volumes, not imported ones)
    if not existing_volume_id:
        CfnOutput(
            scope,
            f"{volume_id}-output",
            value=volume.volume_id,
            description=f"EBS Volume ID for {volume_id}",
            export_name=f"{volume_id}-volume-id",
        )

    return volume


def create_ec2_instance_from_config(
    scope: Construct,
    ec2_config: Dict[str, Any],
    vpc: ec2.Vpc,
    security_groups: List[ec2.SecurityGroup],
    role: iam.Role,
    key_name: str,
    secrets_config: List[Dict[str, Any]] = None,
    persistent_volume: ec2.Volume = None,
) -> ec2.Instance:
    """
    Create an EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        ec2_config: EC2 instance configuration
        vpc: The VPC to place the instance in
        security_groups: Security groups to attach to the instance
        role: IAM role to attach to the instance
        secrets_config: List of secrets to pass as environment variables

    Returns:
        The created EC2 instance
    """
    instance_id = ec2_config.get("id")
    instance_type = ec2_config.get("instance-type", "t2.micro")

    # Create user data to set environment variables and install Docker
    user_data = ec2.UserData.for_linux()

    # Add environment variables for secrets if provided
    if secrets_config:
        env_vars = {}
        for secret in secrets_config:
            secret_id = secret.get("id")
            env_var_name = secret.get("secret-value")
            if secret_id and env_var_name:
                env_vars[env_var_name] = secret_id

        if env_vars:
            user_data.add_commands(
                _generate_env_user_data(
                    env_vars, "/etc/profile.d/orchestrator_secrets.sh"
                )
            )

    # Install Docker, Docker Compose, and other required packages
    user_data.add_commands(
        "yum update -y",
        "yum install -y amazon-linux-extras jq",
        "amazon-linux-extras install docker -y",
        "systemctl start docker",
        "systemctl enable docker",
        "usermod -a -G docker ec2-user",
        'curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose',
        "chmod +x /usr/local/bin/docker-compose",
        "ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose",
        "mkdir -p /opt/orchestrator",
    )

    # Create mount directory based on configuration
    if persistent_volume:
        volume_config = ec2_config.get("ebs", {})
        mount_point = volume_config.get("mount-point", "/var/lib/postgresql/data")
        user_data.add_commands(
            f"mkdir -p {mount_point}",
        )
    else:
        # Default directory creation if no persistent volume
        user_data.add_commands(
            "mkdir -p /var/lib/postgresql/data",
        )

    # Add persistent volume handling if a persistent volume is provided
    if persistent_volume:
        # Get volume configuration from ec2_config
        volume_config = ec2_config.get("ebs", {})
        device = volume_config.get("device", "/dev/xvdf")
        mount_point = volume_config.get("mount-point", "/var/lib/postgresql/data")
        filesystem = volume_config.get("filesystem", "xfs")

        user_data.add_commands(
            # Install AWS CLI for volume operations
            "yum install -y awscli",
            # Get instance metadata
            "INSTANCE_ID=$(curl -s http://169.254.169.254/latest/meta-data/instance-id)",
            "AZ=$(curl -s http://169.254.169.254/latest/meta-data/placement/availability-zone)",
            "REGION=${AZ%?}",  # Remove last character to get region
            f"VOLUME_ID={persistent_volume.volume_id}",
            f"DEVICE={device}",
            f"MOUNT_POINT={mount_point}",
            f"FILESYSTEM={filesystem}",
            # Create log file for debugging
            "exec > >(tee -a /var/log/ebs-setup.log) 2>&1",
            "echo 'Starting EBS volume setup at $(date)'",
            "echo 'Volume ID: $VOLUME_ID'",
            "echo 'Device: $DEVICE'",
            "echo 'Mount Point: $MOUNT_POINT'",
            "echo 'Filesystem: $FILESYSTEM'",
            # Wait for volume to be available
            "echo 'Waiting for EBS volume to be available...'",
            "aws ec2 wait volume-available --volume-ids $VOLUME_ID --region $REGION || echo 'Volume may already be attached'",
            # Check if volume is already attached to this instance
            "ATTACHMENT_STATE=$(aws ec2 describe-volumes --volume-ids $VOLUME_ID --region $REGION --query 'Volumes[0].Attachments[0].State' --output text 2>/dev/null || echo 'None')",
            "if [ '$ATTACHMENT_STATE' != 'attached' ]; then",
            "  echo 'Attaching EBS volume to instance...'",
            "  aws ec2 attach-volume --volume-id $VOLUME_ID --instance-id $INSTANCE_ID --device $DEVICE --region $REGION",
            "  aws ec2 wait volume-in-use --volume-ids $VOLUME_ID --region $REGION",
            "else",
            "  echo 'Volume already attached to this instance'",
            "fi",
            # Wait for device to appear with timeout
            "echo 'Waiting for device to appear...'",
            "TIMEOUT=60",
            "while [ ! -b $DEVICE ] && [ $TIMEOUT -gt 0 ]; do",
            "  sleep 1",
            "  TIMEOUT=$((TIMEOUT-1))",
            "done",
            "if [ ! -b $DEVICE ]; then",
            "  echo 'ERROR: Device $DEVICE did not appear after 60 seconds'",
            "  exit 1",
            "fi",
            # Check if volume has a filesystem, if not create one
            "if ! blkid $DEVICE; then",
            "  echo 'Formatting new EBS volume with $FILESYSTEM...'",
            "  mkfs -t $FILESYSTEM $DEVICE",
            "else",
            "  echo 'EBS volume already has filesystem, skipping format...'",
            "fi",
            # Mount the volume
            "echo 'Mounting EBS volume...'",
            "mount $DEVICE $MOUNT_POINT",
            # Verify mount was successful
            "if mountpoint -q $MOUNT_POINT; then",
            "  echo 'EBS volume successfully mounted'",
            "else",
            "  echo 'ERROR: Failed to mount EBS volume'",
            "  exit 1",
            "fi",
            # Add to fstab for persistent mounting (use UUID for reliability)
            "UUID=$(blkid -s UUID -o value $DEVICE)",
            "echo 'UUID=$UUID $MOUNT_POINT $FILESYSTEM defaults,nofail 0 2' >> /etc/fstab",
            # Set proper ownership and permissions
            "chown -R 999:999 $MOUNT_POINT",
            "chmod 700 $MOUNT_POINT",
            "echo 'EBS volume setup complete at $(date)'",
        )

    # Create the EC2 instance
    instance = ec2.Instance(
        scope,
        instance_id,
        instance_type=ec2.InstanceType(instance_type),
        machine_image=ec2.MachineImage.latest_amazon_linux2(
            cpu_type=ec2.AmazonLinuxCpuType.ARM_64
        ),
        vpc=vpc,
        vpc_subnets=ec2.SubnetSelection(
            subnet_type=ec2.SubnetType.PRIVATE_WITH_EGRESS
        ),  # Use private subnet for better security
        security_group=security_groups[0],  # Primary security group
        role=role,
        user_data=user_data,
        key_pair=ec2.KeyPair.from_key_pair_name(scope, "ImportedKeyPair", key_name),
        block_devices=[
            ec2.BlockDevice(
                device_name="/dev/xvda",
                volume=ec2.BlockDeviceVolume.ebs(
                    volume_size=20,  # 20 GB for Docker images and volumes
                    volume_type=ec2.EbsDeviceVolumeType.GP3,
                ),
            ),
        ],
    )

    # Add additional security groups if any
    for sg in security_groups[1:]:
        instance.add_security_group(sg)

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=ec2_config,
        value=instance.instance_id,
    )

    return instance


def create_ec2_security_group_from_config(
    scope: Construct,
    sg_config: Dict[str, Any],
    vpc: ec2.Vpc,
) -> ec2.SecurityGroup:
    """
    Create a security group for EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        sg_config: Security group configuration
        vpc: The VPC to create the security group in

    Returns:
        The created security group
    """
    sg_id = sg_config.get("id")

    # Create the security group
    security_group = ec2.SecurityGroup(
        scope,
        sg_id,
        security_group_name=sg_id,
        vpc=vpc,
        description=f"Security group for {sg_id}",
    )

    # Apply egress rules from config
    egress_rules = sg_config.get("egress-rules", [])

    if not egress_rules:
        # Default rule if no rules specified in config
        security_group.add_egress_rule(
            ec2.Peer.any_ipv4(), ec2.Port.tcp(443), "Allow HTTPS outbound"
        )
    else:
        for rule in egress_rules:
            peer = _get_peer_from_config(rule.get("peer", "any-ipv4"), security_group)
            port = _get_port_from_config(rule.get("port", {}))
            description = rule.get("description", "Egress rule")

            security_group.add_egress_rule(peer, port, description)

    # Ingress rules will be added dynamically in the OrchestratorStack
    # This allows for more flexible configuration based on the VPC and subnet setup

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=sg_config,
        value=security_group.security_group_id,
    )

    return security_group


def create_ec2_role_from_config(
    scope: Construct,
    role_config: Dict[str, Any],
    secrets_arns: List[str] = None,
) -> iam.Role:
    """
    Create an IAM role for EC2 instance from configuration.

    Args:
        scope: The CDK construct scope
        role_config: IAM role configuration
        secrets_arns: List of secret ARNs to grant access to

    Returns:
        The created IAM role
    """
    role_id = role_config.get("id")

    # Create the role with EC2 as the principal
    role = iam.Role(
        scope,
        role_id,
        role_name=role_id,
        assumed_by=iam.ServicePrincipal("ec2.amazonaws.com"),
        managed_policies=[
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "AmazonSSMManagedInstanceCore"
            ),
            # Add ECR read access for pulling Docker images
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "AmazonEC2ContainerRegistryReadOnly"
            ),
            # Add ECS task execution permissions
            iam.ManagedPolicy.from_aws_managed_policy_name(
                "service-role/AmazonECSTaskExecutionRolePolicy"
            ),
        ],
    )

    # Add permissions to access secrets if provided
    if secrets_arns:
        secrets_policy = iam.PolicyStatement(
            actions=["secretsmanager:GetSecretValue", "secretsmanager:DescribeSecret"],
            resources=secrets_arns,
        )
        role.add_to_policy(secrets_policy)

    # Add permissions to describe EC2 instances (for getting metadata)
    role.add_to_policy(
        iam.PolicyStatement(
            actions=["ec2:DescribeInstances"],
            resources=["*"],
        )
    )

    # Add EBS volume management permissions
    role.add_to_policy(
        iam.PolicyStatement(
            actions=[
                "ec2:DescribeVolumes",
                "ec2:AttachVolume",
                "ec2:DetachVolume",
                "ec2:DescribeVolumeStatus",
                "ec2:DescribeVolumeAttribute",
            ],
            resources=["*"],
        )
    )

    # Add ECS permissions for running and managing tasks (not covered by managed policy)
    role.add_to_policy(
        iam.PolicyStatement(
            actions=[
                "ecs:DescribeTaskDefinition",
                "ecs:DescribeTasks",
                "ecs:TagResource",
                "ecs:ListTasks",
                "ecs:RunTask",
                "ecs:StopTask",
                "ecs:DescribeClusters",
                "ecs:ListClusters",
                "ec2:DescribeVpcs",
                "ec2:DescribeSubnets",
                "ec2:DescribeSecurityGroups",
            ],
            resources=["*"],
        )
    )

    # Add IAM permissions to pass execution role to ECS tasks
    role.add_to_policy(
        iam.PolicyStatement(
            actions=["iam:PassRole"],
            resources=["*"],
            conditions={
                "StringEquals": {"iam:PassedToService": "ecs-tasks.amazonaws.com"}
            },
        )
    )

    # Create SSM parameter if specified
    create_ssm_parameter(
        scope,
        config=role_config,
        value=role.role_arn,
    )

    return role


def _get_peer_from_config(peer_config: str, security_group=None, vpc=None) -> ec2.IPeer:
    """Convert peer config string to ec2.Peer object"""
    if peer_config == "any-ipv4":
        return ec2.Peer.any_ipv4()
    elif peer_config == "any-ipv6":
        return ec2.Peer.any_ipv6()
    elif peer_config == "self" and security_group:
        return ec2.Peer.security_group_id(security_group.security_group_id)
    elif peer_config == "vpc-cidr" and vpc:
        return ec2.Peer.ipv4(vpc.vpc_cidr_block)
    elif peer_config.startswith("cidr:"):
        return ec2.Peer.ipv4(peer_config[5:])
    # Add other peer types as needed
    return ec2.Peer.any_ipv4()  # Default


def _get_port_from_config(port_config: Dict[str, Any]) -> ec2.Port:
    """Convert port config to ec2.Port object"""
    port_type = port_config.get("type", "tcp")
    port_number = port_config.get("number", 443)

    if port_type == "tcp":
        return ec2.Port.tcp(port_number)
    elif port_type == "udp":
        return ec2.Port.udp(port_number)
    elif port_type == "all":
        return ec2.Port.all_traffic()
    # Add other port types as needed
    return ec2.Port.tcp(port_number)  # Default


def _generate_env_user_data(env_vars: Dict[str, str], profile_path: str) -> str:
    """
    Generate user data script to set environment variables.

    Args:
        env_vars: Dictionary of environment variables to set
        profile_path: Path to write the environment variables to

    Returns:
        User data script
    """
    env_exports = "\n".join(
        [f'export {key}="{value}"' for key, value in env_vars.items()]
    )

    return f"""
# Create environment variables file
cat > {profile_path} << 'EOL'
{env_exports}
EOL

# Make it executable
chmod +x {profile_path}

# Create directory for Orchestrator
mkdir -p /opt/orchestrator

"""
