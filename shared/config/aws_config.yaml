# Account ID to deployment mapping
aws-accounts:
  "************":
    deployment-env: dev

deployment-env: "{deployment-env}"
region: "{region}"

infrastructure:
  infrastructure-suffix: "{deployment-env}-{region}"
  stacks:
    prefect:
      id: "[yaml-path-from-stacks]-{infrastructure-suffix}"
      secrets:
        - id: "[yaml-path-from-stacks]-db-user-{infrastructure-suffix}"
          secret-value: "PREFECT_DB_USER"
        - id: "[yaml-path-from-stacks]-db-password-{infrastructure-suffix}"
          secret-value: "PREFECT_DB_PASSWORD"
        - id: "[yaml-path-from-stacks]-ghub-token-{infrastructure-suffix}"
          secret-value: "GHUB_TOKEN"
      lambda:
        id: "[yaml-path-from-stacks]-operational-bootstrap-{infrastructure-suffix}"
        consumer-processes: ["start_bootstrap_lambda"]
        create-parameter-store-variable-with-arn: True
        image: "{aws-account-id}.dkr.ecr.{region}.amazonaws.com/prefect:operational_bootstrap-[tag-number]"
        iam-role:
          id: "[yaml-path-from-stacks]-operational-bootstrap-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
      ec2:
        id: "[yaml-path-from-stacks]-instance-{infrastructure-suffix}"
        instance-type: "t4g.small"
        fully-qualified-domain-name: "prefect.internal.local"
        create-parameter-store-variable-with-arn: True
        # consumer-processes: ["ecs-pool"]
        ebs:
          id: "[yaml-path-from-stacks]-prefect-db-volume-{infrastructure-suffix}"
          size: 10 # Size in GB
          type: "gp3" # EBS volume type (gp3, gp2, io1, io2, st1, sc1)
          mount-point: "/var/lib/postgresql/data"
          filesystem: "xfs" # Filesystem type (xfs, ext4)
          device: "/dev/xvdf" # Device name for attachment
          create-parameter-store-variable-with-arn: True
        security-group:
          id: "[yaml-path-from-stacks]-sg-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          egress-rules:
            - peer: "any-ipv4"
              port:
                type: "tcp"
                number: 443
              description: "Allow HTTPS outbound"
        iam-role:
          id: "[yaml-path-from-stacks]-ec2-role-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
        a-record:
          id: "[yaml-path-from-stacks]-prefect-a-record-{infrastructure-suffix}"
          record-name: "prefect"
          create-parameter-store-variable-with-arn: True
    extraction:
      id: "[yaml-path-from-stacks]-{infrastructure-suffix}"
      buckets:
        - id: "[yaml-path-from-stacks]-source-{infrastructure-suffix}"
          consumer-processes:
            ["balldontlie-api-extraction", "firecrawl-api-extraction"]
      secrets:
        - id: "[yaml-path-from-stacks]-balldontlie-api-key-{infrastructure-suffix}"
          secret-value: "API_KEY_BALLDONTLIE"
          consumer-processes: ["balldontlie-api-extraction"]
        - id: "[yaml-path-from-stacks]-firecrawl-api-key-{infrastructure-suffix}"
          secret-value: "API_KEY_FIRECRAWL"
          consumer-processes: ["firecrawl-api-extraction"]
      cluster:
        id: "[yaml-path-from-stacks]-{infrastructure-suffix}"
        create-parameter-store-variable-with-arn: True
        consumer-processes: ["ecs-pool"]
      tasks:
        # TODO: Change so that points at iam role
        - id: "[yaml-path-from-stacks]-api-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          consumer-processes: ["ecs-pool"]
          parameters:
            cpu: "256"
            memory: "512"
            architecture: "ARM64"
            os: "LINUX"
            container-definitions:
              image: "{aws-account-id}.dkr.ecr.{region}.amazonaws.com/extraction:extraction-latest"
              environment:
                - name: "ENDPOINTS"
                  value: "teams"
      iam:
        - id: "[yaml-path-from-stacks]-api-ecs-task-execution-role-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          consumer-processes: ["ecs-pool", "ecs-extraction-fargate-tasks"]
          enable-local-testing-trust-policy: True
        - id: "[yaml-path-from-stacks]-ecs-task-role-{infrastructure-suffix}"
          consumer-processes: ["ecs-extraction-fargate-tasks"]
          enable-local-testing-trust-policy: True
    network:
      id: "[yaml-path-from-stacks]-{infrastructure-suffix}"
      vpc:
        id: "[yaml-path-from-stacks]-{infrastructure-suffix}"
        create-parameter-store-variable-with-arn: True
        consumer-processes: ["ecs-pool"]
      security-groups:
        - id: "[yaml-path-from-stacks]-ecs-sg-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          consumer-processes: ["ecs-pool"]
          egress-rules:
            - peer: "any-ipv4"
              port:
                type: "all"
              description: "Allow all outbound traffic"
          ingress-rules:
            - peer: "vpc-cidr"
              port:
                type: "all"
              description: "Allow all inbound traffic from self"
        - id: "[yaml-path-from-stacks]-vpc-endpoint-sg-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          consumer-processes: ["vpc-endpoints"]
          egress-rules:
            - peer: "any-ipv4"
              port:
                type: "tcp"
                number: 443
              description: "Allow HTTPS outbound for VPC endpoints"
          ingress-rules:
            - peer: "vpc-cidr"
              port:
                type: "tcp"
                number: 443
              description: "Allow HTTPS inbound from VPC CIDR"
      ec2:
        id: "[yaml-path-from-stacks]-bastion-{infrastructure-suffix}"
        fully-qualified-domain-name: "bastion.internal.local"
        instance-type: "t4g.nano"
        create-parameter-store-variable-with-arn: True
        security-group:
          id: "[yaml-path-from-stacks]-bastion-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
          egress-rules:
            - peer: "any-ipv4"
              port:
                type: "tcp"
                number: 22
              description: "Allow SSH outbound"
        iam-role:
          id: "[yaml-path-from-stacks]-ec2-role-{infrastructure-suffix}"
          create-parameter-store-variable-with-arn: True
        a-record:
          id: "[yaml-path-from-stacks]-bastion-a-record-{infrastructure-suffix}"
          record-name: "bastion"
          create-parameter-store-variable-with-arn: True
      vpc-endpoints:
        # Add data-heavy services here as endpoints are better with data volume.
        # Need to alter the endpoint cdk endpoint creator function for each new endpoint.
        # Security group associated with most of these is ecs-sg
        - id: "[yaml-path-from-stacks]-s3-endpoint-{infrastructure-suffix}"
        - id: "[yaml-path-from-stacks]-ecr-api-endpoint-{infrastructure-suffix}"
        - id: "[yaml-path-from-stacks]-ecr-dkr-endpoint-{infrastructure-suffix}"
        - id: "[yaml-path-from-stacks]-logs-endpoint-{infrastructure-suffix}"
      private-hosted-zone:
        id: "[yaml-path-from-stacks]-private-zone-{infrastructure-suffix}"
        zone-name: "internal.local"
        create-parameter-store-variable-with-arn: True
