import asyncio
from typing import Any, Dict

from prefect import flow, get_client
from prefect.deployments import run_deployment
from prefect.states import StateType


@flow
async def main_orchestration_flow() -> Dict[str, Any]:
    """
    Main orchestration flow that triggers multiple ECS deployment runs sequentially.

    This flow runs deployments in order while yielding control to avoid CPU blocking.
    """
    print("Starting main orchestration flow...")

    # Run first deployment and wait for completion
    flow_run_1 = await _run_deployment_and_wait("extraction/prefect-extraction-task")
    print(f"Flow 1 complete with status: {flow_run_1.state}")

    # Run second deployment and wait for completion
    flow_run_2 = await _run_deployment_and_wait("extraction/prefect-extraction-task")
    print(f"Flow 2 complete with status: {flow_run_2.state}")

    return _build_result(flow_run_1, flow_run_2)


async def _run_deployment_and_wait(deployment_name: str):
    """
    Run a deployment and wait for completion without blocking CPU.

    Args:
        deployment_name: Name of the deployment to run

    Returns:
        FlowRun object with final state
    """
    print(f"Triggering deployment: {deployment_name}")

    # Start the deployment without waiting (timeout=0)
    flow_run = await run_deployment(name=deployment_name, timeout=0)
    print(f"Started flow run {flow_run.id}, waiting for completion...")

    # Wait for completion using async polling
    return await _wait_for_flow_completion(flow_run.id)


async def _wait_for_flow_completion(flow_run_id: str, poll_interval: int = 5):
    """
    Wait for a flow run to complete by polling its status asynchronously.

    Args:
        flow_run_id: ID of the flow run to monitor
        poll_interval: Seconds to wait between status checks

    Returns:
        FlowRun object with final state
    """
    async with get_client() as client:
        while True:
            flow_run = await client.read_flow_run(flow_run_id)

            # Check if flow run is in a terminal state
            if flow_run.state.type in [
                StateType.COMPLETED,
                StateType.FAILED,
                StateType.CANCELLED,
                StateType.CRASHED,
            ]:
                return flow_run

            print(f"Flow run {flow_run_id} status: {flow_run.state.type}, waiting...")
            # Use asyncio.sleep to yield control and avoid blocking CPU
            await asyncio.sleep(poll_interval)


def _build_result(flow_run_1, flow_run_2) -> Dict[str, Any]:
    """Build the return result dictionary."""
    return {
        "run1_id": str(flow_run_1.id),
        "run2_id": str(flow_run_2.id),
        "run1_status": str(flow_run_1.state),
        "run2_status": str(flow_run_2.state),
    }
